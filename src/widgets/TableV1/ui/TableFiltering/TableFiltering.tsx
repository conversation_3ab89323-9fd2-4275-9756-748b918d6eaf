import { Filter, FilteringState, IntegratedFiltering } from '@devexpress/dx-react-grid'
import { TableFilterRow } from '@devexpress/dx-react-grid-material-ui'
import { useMemo } from 'react'
import { IBaseRowData, IColumn } from 'widgets/TableV1'

export interface TableFilteringProps<RowData extends IBaseRowData> {
  columns: IColumn<RowData>[]
  columnSearchDisabled: string[]
  isSearchMode: boolean
  filters: Filter[]
  setFilters: (filters: Filter[]) => void
}

export const TableFiltering = <RowData extends IBaseRowData>(props: TableFilteringProps<RowData>) => {
  const { columns, columnSearchDisabled, isSearchMode, filters, setFilters } = props

  const filteringColumnExtensions: IntegratedFiltering.ColumnExtension[] = useMemo(() => {
    const arr: IntegratedFiltering.ColumnExtension[] = []
    for (const column of columns) {
      if (column.onSearch) {
        arr.push({
          columnName: column.name,
          predicate: column.onSearch,
        })
      }
    }

    return arr
  }, [columns])

  const filteringStateColumnExtensions = useMemo(
    () =>
      columnSearchDisabled.map((el) => ({
        columnName: String(el),
        filteringEnabled: false,
      })),
    [columnSearchDisabled],
  )

  return (
    <>
      {/* Плагин, управляющий состоянием фильтрации. */}
      <FilteringState
        defaultFilters={[]}
        filters={filters}
        onFiltersChange={setFilters}
        columnExtensions={filteringStateColumnExtensions}
      />
      <IntegratedFiltering columnExtensions={filteringColumnExtensions} />
      {/* Плагин, отображающий строку фильтра. */}
      {isSearchMode && (
        <TableFilterRow
          messages={{
            filterPlaceholder: 'Поиск',
          }}
        />
      )}
    </>
  )
}
