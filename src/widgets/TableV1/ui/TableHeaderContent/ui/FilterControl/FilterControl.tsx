import FilterAltIcon from '@mui/icons-material/FilterAlt'
import { Menu, Tooltip } from '@mui/material'
import { useTheme } from 'app/providers/ThemeProvider'
import { MouseEvent, useState } from 'react'
import { IBaseRowData, IColumn } from 'widgets/TableV1'
import {
  FilterControlRadioSelect,
  FilterControlRadioSelectProps,
  TFilterControlRadioSelectFiltering,
} from 'widgets/TableV1/ui/TableHeaderContent/ui/FilterControl/ui/FilterControlRadioSelect'

import cls from './FilterControl.module.scss'
import {
  FilterControlMultiSelect,
  FilterControlMultiSelectProps,
  FilterControlSwitch,
  FilterControlSwitchProps,
  FilterControlTreeSelect,
  FilterControlTreeSelectProps,
  TFilterControlMultiSelectFiltering,
  TFilterControlSwitchFiltering,
  TFilterControlTreeSelectFiltering,
} from './ui'

export type TTableColumnFiltering =
  | TFilterControlMultiSelectFiltering
  | TFilterControlRadioSelectFiltering
  | TFilterControlSwitchFiltering
  | TFilterControlTreeSelectFiltering

export interface FilterControlProps<RowData extends IBaseRowData> {
  column: Omit<IColumn<RowData>, 'width'>
  onFilter?:
    | FilterControlMultiSelectProps['onFilter']
    | FilterControlRadioSelectProps['onFilter']
    | FilterControlSwitchProps['onFilter']
    | FilterControlTreeSelectProps['onFilter']
}

export const FilterControl = <RowData extends IBaseRowData>(props: FilterControlProps<RowData>) => {
  const { column, onFilter } = props
  const [anchorFilter, setAnchorFilter] = useState<null | HTMLElement>(null)
  const { theme } = useTheme()

  const handleClickFilter = (event: MouseEvent<HTMLElement>) => {
    setAnchorFilter(event.currentTarget)
    setTimeout(() => {
      const menuRef = document.getElementById('account-menu')
      if (menuRef) {
        menuRef.classList.add(theme)
        menuRef.style.background = 'transparent !important'
      }
    }, 0)
  }

  const handleCloseFilter = () => {
    setAnchorFilter(null)
  }

  return (
    <>
      {column.filtering && (
        <Tooltip title='Фильтрация'>
          <button type='button' onClick={handleClickFilter} className={cls.button}>
            <FilterAltIcon />
          </button>
        </Tooltip>
      )}
      <Menu
        anchorEl={anchorFilter}
        id='account-menu'
        open={!!anchorFilter}
        onClose={handleCloseFilter}
        transformOrigin={{
          horizontal: 'right',
          vertical: 'top',
        }}
        anchorOrigin={{
          horizontal: 'right',
          vertical: 'bottom',
        }}
      >
        {column.filtering?.type === 'switch' && (
          <FilterControlSwitch
            columnName={column.name}
            filtering={column.filtering}
            onFilter={onFilter as FilterControlSwitchProps['onFilter']}
          />
        )}
        {column.filtering?.type === 'multiselect' && (
          <FilterControlMultiSelect
            columnName={column.name}
            filtering={column.filtering}
            onFilter={onFilter as FilterControlMultiSelectProps['onFilter']}
          />
        )}
        {column.filtering?.type === 'radioselect' && (
          <FilterControlRadioSelect
            columnName={column.name}
            filtering={column.filtering}
            onFilter={onFilter as TFilterControlRadioSelectFiltering['onFilter']}
          />
        )}
        {column.filtering?.type === 'treeselect' && (
          <FilterControlTreeSelect
            columnName={column.name}
            filtering={column.filtering}
            onFilter={onFilter as FilterControlTreeSelectProps['onFilter']}
          />
        )}
      </Menu>
    </>
  )
}
