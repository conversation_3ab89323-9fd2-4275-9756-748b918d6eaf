import { FormControlLabel, MenuItem, Radio } from '@mui/material'
import { ChangeEvent, FC } from 'react'
import { classNames } from 'shared/lib/classNames'

import cls from '../../FilterControl.module.scss'

interface IFilterControlSelectItem {
  value: string
  label: string
  checked: boolean
}

export type TFilterControlRadioSelectFiltering = {
  type: 'radioselect'
  items: IFilterControlSelectItem[]
  onFilter?: (value: string) => void
}

export interface FilterControlRadioSelectProps {
  columnName: string
  filtering: TFilterControlRadioSelectFiltering
  onFilter?: (type: TFilterControlRadioSelectFiltering['type'], columnName: string, value: string) => void
}

export const FilterControlRadioSelect: FC<FilterControlRadioSelectProps> = (props) => {
  const { columnName, filtering, onFilter } = props

  const handleChangeFilter = (event: ChangeEvent<HTMLInputElement>) => {
    const item = filtering.items.find((item) => item.value === event.target.name)
    if (item) {
      item.checked = event.target.checked
      if (filtering.onFilter) {
        filtering?.onFilter(item.value)
      }
      if (onFilter) {
        onFilter('radioselect', columnName, item.value)
      }
    }
  }

  return (
    <>
      {filtering.items.map((item) => (
        <MenuItem key={item.value} className={classNames(cls.filterItem, {}, [])}>
          <FormControlLabel
            control={<Radio checked={item.checked} name={item.value} onChange={handleChangeFilter} />}
            label={item.label}
          />
        </MenuItem>
      ))}
    </>
  )
}
