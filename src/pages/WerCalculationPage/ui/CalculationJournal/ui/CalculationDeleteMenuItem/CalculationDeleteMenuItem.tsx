import { MenuItem } from '@mui/material'
import { observer } from 'mobx-react'
import { FC, useState } from 'react'
import { Icon } from 'shared/ui'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'

import { IWerCalculationJournalStore } from '../../model'
import { CalculationRecordButtonProps } from '../CalculationRecordButton'
import cls from './CalculationDeleteMenuItem.module.scss'

type CalculationRecord = IWerCalculationJournalStore['calculations'][0]

interface CalculationDeleteMenuItemProps {
  row: CalculationRecord
  onDelete: CalculationRecordButtonProps['onDelete']
}

export const CalculationDeleteMenuItem: FC<CalculationDeleteMenuItemProps> = observer((props) => {
  const { row, onDelete } = props
  const [opened, setOpened] = useState(false)
  const [loading, setLoading] = useState(false)

  const openModal = () => setOpened(true)

  const closeModal = () => setOpened(false)

  const handleDelete = () => {
    setLoading(true)
    onDelete(row.tabId)
      .then(() => {
        closeModal()
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <>
      <MenuItem onClick={openModal} className={cls.menuItem}>
        <Icon name='trash' height={16} width={16} className={cls.deleteIcon} />
      </MenuItem>
      {opened && (
        <Modal
          open
          title='Удаление расчёта'
          onClose={closeModal}
          actions={
            <div className={cls.modalActions}>
              <LoadingButton variant='contained' loading={loading} onClick={handleDelete} className={cls.button}>
                Продолжить
              </LoadingButton>
            </div>
          }
        >
          <div className={cls.modalBody}>
            <p>Расчёт "{row.title}" будет удален</p>
            <p>безвозвратно</p>
          </div>
        </Modal>
      )}
    </>
  )
})
