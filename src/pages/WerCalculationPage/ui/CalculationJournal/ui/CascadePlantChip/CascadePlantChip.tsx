import CloseIcon from '@mui/icons-material/Close'
import Box from '@mui/material/Box/Box'
import Chip from '@mui/material/Chip/Chip'
import { classNames } from 'shared/lib/classNames'
import { ItemsProps } from 'shared/ui/Select/Select.tsx'

import cls from './CascadePlantChip.module.scss'

interface CascadePlantChipProps<TData extends ItemsProps> {
  selected: unknown
  convertIdsToTData: (ids: number[]) => TData[]
}

export const CascadePlantChip = <TData extends ItemsProps>(props: CascadePlantChipProps<TData>) => {
  const { selected, convertIdsToTData } = props

  return (
    <Box className={cls.container}>
      {Array.isArray(selected) &&
        convertIdsToTData(selected.map(Number)).map((value) => (
          <Chip
            key={value.value}
            className={classNames(
              cls.chip,
              {
                [cls.error]: value.error,
              },
              [],
            )}
            label={value.label}
            deleteIcon={<CloseIcon className={cls.deleteIcon} fontSize='small' />}
          />
        ))}
    </Box>
  )
}
