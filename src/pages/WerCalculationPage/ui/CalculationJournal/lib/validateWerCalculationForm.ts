import { isAfter, isBefore, isValid } from 'date-fns'

import { ICalculationFormData, IWerCalculationJournalStore, werCalculationJournalErrorObjectInit } from '../model'

export const validateWerCalculationForm = (formData: ICalculationFormData) => {
  const errors: IWerCalculationJournalStore['formErrors'] = structuredClone(werCalculationJournalErrorObjectInit)
  // Валидация названия
  if (!formData.title) {
    errors['title'].push('Поле должно быть заполнено')
  }
  if (formData.title.length > 100) {
    errors['title'].push('Название должно содержать не более 100 символов')
  }

  // Валидация ГЭС / каскад ГЭС
  if (formData.plantsCascades.length === 0) {
    errors['plantsCascades'].push('Поле должно быть заполнено')
  }

  // Валидация даты начала и конца расчета
  if (!formData.startDate || !isValid(new Date(formData.startDate))) {
    errors['startDate'].push('Поле должно быть заполнено')
  }
  if (!formData.endDate || !isValid(new Date(formData.endDate))) {
    errors['endDate'].push('Поле должно быть заполнено')
  }
  if (isAfter(new Date(formData.startDate), new Date(formData.endDate))) {
    errors['startDate'].push('Дата начала должна быть раньше даты окончания')
    errors['endDate'].push('Дата окончания должна быть позже даты начала')
  }

  // Валидация общей даты начала и конца расчета
  if (formData.overallPeriod && (!formData.overallStartDate || !isValid(new Date(formData.overallStartDate)))) {
    errors['overallStartDate'].push('ОПоле должно быть заполнено')
  }
  if (isAfter(new Date(formData.overallStartDate), new Date(formData.overallEndDate))) {
    errors['overallStartDate'].push('Общая дата начала должна быть раньше общей даты окончания')
    errors['overallEndDate'].push('Общая дата окончания должна быть позже общей даты начала')
  }
  if (isAfter(new Date(formData.overallStartDate), new Date(formData.startDate))) {
    errors['overallStartDate'].push('Общая дата начала должна быть раньше или равна дате окончания расчета')
  }
  if (isBefore(new Date(formData.overallEndDate), new Date(formData.endDate))) {
    errors['overallEndDate'].push('Общая дата окончания должна быть позже или равна дате окончания расчета')
  }

  // Валидация описания
  if (formData?.description.length > 300) {
    errors['description'].push('Описание должно содержать не более 300 символов')
  }

  // Валидация боевых станций
  if (formData.objective && formData.objectivePlants.length === 0) {
    errors['objectivePlants'].push('Должно быть выбрано не менее одной станции')
  }

  return errors
}
