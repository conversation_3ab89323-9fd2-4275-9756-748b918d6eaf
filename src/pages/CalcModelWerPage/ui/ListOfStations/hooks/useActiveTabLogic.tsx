import { useMemo } from 'react'
import { IWerListOfStationsStore } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerListOfStationsStore.types'
import { useStore } from 'stores/useStore'

import { Characteristics } from '../ui/MainSection/ui/RightContainerLayout/ui/Characteristics'
import { Parameters } from '../ui/MainSection/ui/RightContainerLayout/ui/Parameters'
import { Restrictions } from '../ui/MainSection/ui/RightContainerLayout/ui/Restrictions'

interface TabConfig {
  [key: string]: {
    component: JSX.Element
    onSave: () => Promise<void>
    onReset: () => void
    isModified: boolean
  }
}

export const useActiveTabLogic = (activeTab: IWerListOfStationsStore['tabs'][0]['value']) => {
  const {
    calcModelWerStore: {
      listOfStationsStore: { parametersStore, restrictionsStore, characteristicsStore },
    },
  } = useStore()
  const { isPlantCascadesModified, resetPlantCascades, savePlantCascades } = parametersStore
  const { isEditing, resetChanges, saveChanges } = restrictionsStore
  const { reservoirVolumeStore, tailraceStore, specificConsumptionStore, activeSubTab } = characteristicsStore

  const getCharacteristicsHandlers = () => {
    switch (activeSubTab) {
      case 'reservoirVolumes':
        return {
          onSave: reservoirVolumeStore.saveChangedCharacteristicsSpreadsheetData,
          onReset: reservoirVolumeStore.resetCharacteristicsSpreadsheetData,
          isModified: reservoirVolumeStore.isEditRows,
        }
      case 'specificConsumption':
        return {
          onSave: specificConsumptionStore.saveChangedCharacteristicsSpreadsheetData,
          onReset: specificConsumptionStore.resetCharacteristicsSpreadsheetData,
          isModified: specificConsumptionStore.isEditRows,
        }
      case 'tailwaterLevelConsumptionRelations':
        return {
          onSave: tailraceStore.saveChangedCharacteristicsSpreadsheetData,
          onReset: tailraceStore.resetCharacteristicsSpreadsheetData,
          isModified: tailraceStore.isEditRows,
        }
      default:
        return {
          onSave: () => Promise.resolve(),
          onReset: () => {},
          isModified: false,
        }
    }
  }
  const tabConfig: TabConfig = useMemo(
    () => ({
      restrictions: {
        component: <Restrictions />,
        onSave: saveChanges,
        onReset: resetChanges,
        isModified: isEditing,
      },
      parameters: {
        component: <Parameters />,
        onSave: savePlantCascades,
        onReset: resetPlantCascades,
        isModified: isPlantCascadesModified,
      },
      characteristics: {
        component: <Characteristics />,
        ...getCharacteristicsHandlers(),
      },
    }),
    [
      isEditing,
      isPlantCascadesModified,
      activeSubTab,
      reservoirVolumeStore.isEditRows,
      tailraceStore.isEditRows,
      specificConsumptionStore.isEditRows,
    ],
  )

  return tabConfig[activeTab] || null
}
