import { add, isBefore, parse, sub } from 'date-fns'
import { describe, expect, it } from 'vitest'

// Тестируем логику навигации по периодам
describe('Restrictions period navigation', () => {
  // Шаг сдвига при нажатии Следующего/Предыдущего периода: +1 месяц и +2 недели
  const STEP_DAYS = 44 // 30 + 14

  it('should correctly calculate the next period and return to the initial one', () => {
    // Начальный период
    const initialFrom = new Date('2025-06-28T00:00:00.000Z')
    const initialTo = new Date('2025-08-12T00:00:00.000Z')

    // Следующий период
    const nextFrom = add(initialFrom, { days: STEP_DAYS })
    const nextTo = add(initialTo, { days: STEP_DAYS })

    // Проверяем, что новые даты рассчитаны верно
    expect(nextFrom).toEqual(new Date('2025-08-11T00:00:00.000Z'))
    expect(nextTo).toEqual(new Date('2025-09-25T00:00:00.000Z'))

    // Возврат к предыдущему периоду
    const backFrom = sub(nextFrom, { days: STEP_DAYS })
    const backTo = sub(nextTo, { days: STEP_DAYS })

    // Должны получить исходные даты, так как период не должен дрейфовать
    expect(backFrom).toEqual(initialFrom)
    expect(backTo).toEqual(initialTo)
  })

  it('should handle multiple navigation steps consistently', () => {
    const initialFrom = new Date('2025-06-28T00:00:00.000Z')
    const initialTo = new Date('2025-08-12T00:00:00.000Z')
    let from = initialFrom
    let to = initialTo

    // Несколько шагов вперед
    for (let i = 0; i < 5; i++) {
      from = add(from, { days: STEP_DAYS })
      to = add(to, { days: STEP_DAYS })
    }

    // и столько же шагов назад
    for (let i = 0; i < 5; i++) {
      from = sub(from, { days: STEP_DAYS })
      to = sub(to, { days: STEP_DAYS })
    }

    // Должны вернуться точно к исходным датам
    expect(from).toEqual(initialFrom)
    expect(to).toEqual(initialTo)
  })
})

// Тестируем логику фильтрации сезонных ограничений

// Вспомогательная функция, которая ТОЧНО копирует логику из компонента Restrictions.
// Это позволяет не дублировать код и тестировать именно то, что используется в `useMemo`.
const isRestrictionVisible = (period: [Date, Date], restriction: { beginDate: string; stopDate: string }): boolean => {
  const [from, to] = period
  const { beginDate, stopDate } = restriction

  if (beginDate && stopDate) {
    const fmt = "'--'MM-dd"
    const fromYear = from.getFullYear()
    const toYear = to.getFullYear()

    for (let year = fromYear - 1; year <= toYear; year++) {
      const baseDate = new Date(year, 0, 1)
      const b0 = parse(beginDate, fmt, baseDate)
      let e0 = parse(stopDate, fmt, baseDate)

      if (isBefore(e0, b0)) {
        e0 = add(e0, { years: 1 })
      }

      if (!(e0 < from || b0 > to)) {
        return true
      }
    }
  }

  return false
}

describe('Seasonal restrictions filtering', () => {
  it('should show restriction within the same year', () => {
    const period: [Date, Date] = [new Date('2025-03-01'), new Date('2025-03-31')]
    const restriction = { beginDate: '--03-12', stopDate: '--03-13' }
    expect(isRestrictionVisible(period, restriction)).toBe(true)
  })

  it('should not show restriction outside the period', () => {
    const period: [Date, Date] = [new Date('2025-06-01'), new Date('2025-08-31')]
    const restriction = { beginDate: '--03-12', stopDate: '--03-13' }
    expect(isRestrictionVisible(period, restriction)).toBe(false)
  })

  it('should show restriction when period crosses into a new year', () => {
    const period: [Date, Date] = [new Date('2024-12-15'), new Date('2025-01-15')]
    const restriction = { beginDate: '--12-20', stopDate: '--01-10' }
    expect(isRestrictionVisible(period, restriction)).toBe(true)
  })

  it('should show restriction that started last year and ends in the current period', () => {
    // Это ключевой тест, который проверяет правильность логики `fromYear - 1`
    const period: [Date, Date] = [new Date('2025-02-01'), new Date('2025-02-28')]
    const restriction = { beginDate: '--12-20', stopDate: '--02-10' }
    expect(isRestrictionVisible(period, restriction)).toBe(true)
  })

  it('should not show a "winter" restriction that is not in the current period', () => {
    const period: [Date, Date] = [new Date('2025-06-01'), new Date('2025-07-01')]
    const restriction = { beginDate: '--12-20', stopDate: '--02-10' }
    expect(isRestrictionVisible(period, restriction)).toBe(false)
  })
})
