import { add, format, parse, sub } from 'date-fns'

// Демонстрация исправленной логики навигации по периодам
console.log('=== Демонстрация навигации по периодам ===')

const STEP_DAYS = 30 + 14 // 44 дня

// Исходный период
let from = new Date('2025-06-28')
let to = new Date('2025-08-12')

console.log(`Исходный период: ${format(from, 'dd.MM.yyyy')} – ${format(to, 'dd.MM.yyyy')}`)

// Следующий период
from = add(from, { days: STEP_DAYS })
to = add(to, { days: STEP_DAYS })
console.log(`Следующий период: ${format(from, 'dd.MM.yyyy')} – ${format(to, 'dd.MM.yyyy')}`)

// Предыдущий период (возврат)
from = sub(from, { days: STEP_DAYS })
to = sub(to, { days: STEP_DAYS })
console.log(`Предыдущий период: ${format(from, 'dd.MM.yyyy')} – ${format(to, 'dd.MM.yyyy')}`)

// Снова следующий период
from = add(from, { days: STEP_DAYS })
to = add(to, { days: STEP_DAYS })
console.log(`Снова следующий период: ${format(from, 'dd.MM.yyyy')} – ${format(to, 'dd.MM.yyyy')}`)

console.log('\n=== Демонстрация фильтрации сезонных ограничений ===')

// Функция для проверки сезонного ограничения
function checkSeasonalRestriction(
  periodFrom: Date,
  periodTo: Date,
  beginDate: string,
  stopDate: string
): boolean {
  const fmt = "'--'MM-dd"
  const fromYear = periodFrom.getFullYear()
  const toYear = periodTo.getFullYear()

  for (let year = fromYear; year <= toYear + 1; year++) {
    const baseDate = new Date(year, 0, 1)
    const b0 = parse(beginDate, fmt, baseDate)
    let e0 = parse(stopDate, fmt, baseDate)

    // если сезон переходит на следующий год
    if (e0 < b0) {
      e0 = add(e0, { years: 1 })
    }

    if (!(e0 < periodFrom || b0 > periodTo)) {
      return true
    }
  }

  return false
}

// Тестовые данные
const testPeriod: [Date, Date] = [new Date('2025-03-01'), new Date('2025-03-31')]
const restrictions = [
  { beginDate: '--03-12', stopDate: '--03-13', description: 'Ограничение в марте' },
  { beginDate: '--12-20', stopDate: '--01-10', description: 'Зимнее ограничение (дек-янв)' },
  { beginDate: '--06-01', stopDate: '--08-31', description: 'Летнее ограничение' }
]

console.log(`Период фильтрации: ${format(testPeriod[0], 'dd.MM.yyyy')} – ${format(testPeriod[1], 'dd.MM.yyyy')}`)

restrictions.forEach(restriction => {
  const shouldShow = checkSeasonalRestriction(
    testPeriod[0],
    testPeriod[1],
    restriction.beginDate,
    restriction.stopDate
  )
  console.log(`${restriction.description}: ${shouldShow ? 'ПОКАЗАТЬ' : 'СКРЫТЬ'}`)
})

export {}
