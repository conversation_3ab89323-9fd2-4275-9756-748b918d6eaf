import { I<PERSON><PERSON><PERSON>on, Tooltip } from '@mui/material'
import { add, differenceInDays, endOfDay, format, isAfter, isBefore, parse, parseISO, startOfDay, sub } from 'date-fns'
import { IWerRestriction } from 'entities/api/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { AddAndEditRestrictionModal } from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Restrictions/ui/AddAndEditRestrictionModal'
import { useEffect, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { formatDate, readableISO8601DateMonth } from 'shared/lib/dateFormates'
import { AccessControl } from 'shared/ui/AccessControl'
import { Button } from 'shared/ui/Button'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { IWerRestrictionPrePost } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore/WerRestrictionsStore.types'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import { useNotifyDayChanged } from '../../hooks/useNotifyDayChanged'
import cls from './Restriction.module.scss'

export const Restrictions = observer(() => {
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [isAddModal, setIsAddModal] = useState<boolean>(false)
  const [editableRestriction, setEditableRestriction] = useState<IWerRestrictionPrePost | null>(null)
  const [height, setHeight] = useState<number | null>(null)
  const isDayChanged = useNotifyDayChanged()
  const {
    calcModelWerStore: {
      date,
      selectedPlant,
      listOfStationsStore: { restrictionsStore },
    },
  } = useStore()
  const { isLoading, restrictions, originalRestrictions, init, deleteRestriction, reset, resetChanges } =
    restrictionsStore

  const disableAddEditRestriction = useMemo(
    () => !isAfter(startOfDay(date), endOfDay(new Date())) || selectedPlant?.viewOnly,
    [date, isDayChanged, selectedPlant],
  )

  const changeHeightTable = () => {
    if (tableContainerRef && tableContainerRef.current) {
      const el = tableContainerRef.current.querySelector('div')
      if (el) {
        setHeight(el.getBoundingClientRect().height)
      }
    }
  }

  // Дефолтный период. Зависит от даты, выбранной в сайдбаре
  const defaultPeriod = useMemo<[Date, Date]>(() => {
    const from = sub(date, { weeks: 2 })
    const to = add(date, { months: 1 })

    return [from, to]
  }, [date])

  // Вычисляем, сколько дней в нашем дефолтном периоде
  const defaultDurationInDays = useMemo(() => {
    return differenceInDays(defaultPeriod[1], defaultPeriod[0])
  }, [defaultPeriod])

  const [period, setPeriod] = useState<[Date, Date]>(defaultPeriod)

  const handlePrevPeriod = () => {
    setPeriod(([from, _]) => {
      // Новая дата окончания = текущая дата начала - 1 день
      const newTo = sub(from, { days: 1 })
      // Новая дата начала = новая дата окончания - дефолтный промежуток
      const newFrom = sub(newTo, { days: defaultDurationInDays })

      return [newFrom, newTo]
    })
  }

  const handleNextPeriod = () => {
    setPeriod(([_, to]) => {
      // Новая дата начала = текущая дата окончания + 1 день
      const newFrom = add(to, { days: 1 })
      // Новая дата окончания = новая дата начала + дефолтный промежуток
      const newTo = add(newFrom, { days: defaultDurationInDays })

      return [newFrom, newTo]
    })
  }

  const filteredRestrictions = useMemo(() => {
    const [from, to] = period

    return restrictions.filter((r) => {
      // ALL_YEAR — всегда показываем? Есть с датами и без
      if (r.category.code === 'ALL_YEAR') return true

      // TEMPORARY — сравниваем по полным датам
      if (r.category.code === 'TEMPORARY' && r.temporaryBeginDate && r.temporaryEndDate) {
        const b = parseISO(r.temporaryBeginDate)
        const e = parseISO(r.temporaryEndDate)

        // Стандартная проверка на пересечение интервалов
        return !(e < from || b > to)
      }

      // SEASONAL или иной «месяц-день» в формате '--MM-dd'
      if (r.beginDate && r.stopDate) {
        const fmt = "'--'MM-dd"
        const fromYear = from.getFullYear()
        const toYear = to.getFullYear()

        // Итерируем по годам, которые могут быть затронуты.
        // Начинаем с fromYear - 1, чтобы поймать сезоны, переходящие с прошлого года.
        // Заканчиваем на toYear, т.к. нет смысла проверять год после окончания периода.
        for (let year = fromYear - 1; year <= toYear; year++) {
          const baseDate = new Date(year, 0, 1) // 1 января проверяемого года
          const b0 = parse(r.beginDate, fmt, baseDate)
          let e0 = parse(r.stopDate, fmt, baseDate)

          // если сезон переходит на следующий год (например, с декабря на январь)
          if (isBefore(e0, b0)) {
            e0 = add(e0, { years: 1 })
          }

          // Проверяем пересечение этого "материализованного" сезонного
          // интервала с нашим основным периодом [from, to]
          if (!(e0 < from || b0 > to)) {
            return true // Нашли пересечение, можно дальше не искать
          }
        }

        return false // Не нашли пересечений ни в одном из релевантных годов
      }

      return false
    })
  }, [restrictions, period])

  useEffect(() => {
    setPeriod(defaultPeriod)
  }, [defaultPeriod, selectedPlant])

  useEffect(() => {
    changeHeightTable()
    window.addEventListener('resize', changeHeightTable)

    return () => {
      reset()
      resetChanges()
      window.removeEventListener('resize', changeHeightTable)
    }
  }, [])

  useEffect(() => {
    if (selectedPlant?.plantId) {
      resetChanges()
      init(selectedPlant?.plantId, format(date, 'yyyy-MM-dd'))
    }
  }, [selectedPlant, date])

  const columns = [
    {
      name: 'parameter',
      title: 'Параметр',
      width: 135,
      editingEnabled: false,
      render: (value: IWerRestriction['parameter']) => value.title,
    },
    {
      name: 'category',
      title: 'Категория',
      width: 100,
      editingEnabled: false,
      render: (value: IWerRestriction['category']) => value.title,
    },
    {
      name: 'type',
      title: 'Тип ограничения',
      width: 130,
      editingEnabled: false,
      render: (value: IWerRestriction['type']) => value.title,
    },
    {
      name: 'beginDate',
      title: 'Дата начала',
      width: 120,
      editingEnabled: false,
      render: (_: IWerRestriction['beginDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryBeginDate) {
          return formatDate(row.temporaryBeginDate)
        } else if (row.beginDate) {
          return readableISO8601DateMonth(row.beginDate)
        }

        return ''
      },
    },
    {
      name: 'stopDate',
      title: 'Дата окончания',
      width: 120,
      editingEnabled: false,
      render: (_: IWerRestriction['stopDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryEndDate) {
          return formatDate(row.temporaryEndDate)
        } else if (row.stopDate) {
          return readableISO8601DateMonth(row.stopDate)
        }

        return ''
      },
    },
    {
      name: 'minValue',
      title: 'Мин.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['minValue']) => value || '',
    },
    {
      name: 'maxValue',
      title: 'Макс.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['maxValue']) => value || '',
    },
    {
      name: 'valueType',
      title: 'Тип значения',
      width: 150,
      editingEnabled: false,
      render: (value: IWerRestriction['valueType']) => (value ? value.title : ''),
    },
    {
      name: 'comment',
      title: 'Комментарий',
      width: 200,
      editingEnabled: false,
      render: (value: IWerRestriction['comment']) => value || '',
    },
    {
      name: 'actions',
      title: '',
      width: 80,
      editingEnabled: false,
      headRender: () => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actionHeader}>
              <Tooltip title='Создать ограничение'>
                <span>
                  <IconButton
                    sx={{ color: 'var(--primary-color)', display: 'inline-flex!important' }}
                    onClick={() => {
                      setIsAddModal(true)
                    }}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon className={cls.addIcon} name='plus' width={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
      render: (_: never, value: IWerRestrictionPrePost) => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actions}>
              <Tooltip title='Настроить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      setEditableRestriction(value)
                    }}
                    className={classNames(cls.iconBlueBtn)}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='settings' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title='Удалить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      deleteRestriction(value)
                    }}
                    className={cls.iconRedBtn}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='trash' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
    },
  ]

  const handleClose = () => {
    setIsAddModal(false)
    setEditableRestriction(null)
  }

  return (
    <>
      {selectedPlant ? (
        <div className={cls.fullHeight}>
          <div className={cls.periodPickerContainer}>
            <div className={cls.dateRangePickerContainer}>
              <DateRangePicker
                dateFrom={period[0]}
                dateTo={period[1]}
                handleChangeDate={(value) => {
                  if (value[0] && value[1]) {
                    setPeriod([value[0], value[1]])
                  }
                }}
              />
            </div>

            <div className={cls.periodButtons}>
              <Button size='small' variant='outlined' onClick={handlePrevPeriod}>
                <Icon width={12} name='arrowLeft' />
                Предыдущий период
              </Button>
              <Button size='small' variant='outlined' onClick={handleNextPeriod}>
                Следующий период
                <Icon width={12} className={cls.iconNext} name='arrowLeft' />
              </Button>
            </div>
          </div>
          <div ref={tableContainerRef} className={cls.tableWrapper}>
            <Table
              rows={filteredRestrictions}
              columns={columns}
              height={height ?? 500}
              initialData={originalRestrictions ?? []}
              editMode
              columnSearchDisabled={[
                'parameter',
                'category',
                'type',
                'beginDate',
                'stopDate',
                'minValue',
                'maxValue',
                'valueType',
                'comment',
                'actions',
              ]}
              loading={isLoading}
            />
          </div>
        </div>
      ) : (
        <div className={cls.noData}>Выберите станцию</div>
      )}
      {isAddModal && <AddAndEditRestrictionModal onClose={handleClose} />}
      {editableRestriction && <AddAndEditRestrictionModal onClose={handleClose} defaultValue={editableRestriction} />}
    </>
  )
})
