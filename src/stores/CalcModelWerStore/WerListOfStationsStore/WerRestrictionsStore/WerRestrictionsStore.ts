import { format } from 'date-fns'
import {
  IParameter,
  IWerRestriction,
  IWerRestrictionInput,
  IWerRestrictionOptions,
} from 'entities/api/calcModelPage.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { TActions } from 'entities/shared/common.entities.ts'
import { makeAutoObservable, runInAction } from 'mobx'
import api from 'shared/api'
import { deepCloneArray } from 'shared/lib/deepClone'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { RootStore } from 'stores/RootStore.ts'

import { IWerRestrictionsStore } from './WerRestrictionsStore.types'

export class WerRestrictionsStore implements IWerRestrictionsStore {
  rootStore: IWerRestrictionsStore['rootStore']
  isLoading: IWerRestrictionsStore['isLoading'] = true
  isEditing: IWerRestrictionsStore['isEditing'] = false
  restrictions: IWerRestrictionsStore['restrictions'] = []
  originalRestrictions: IWerRestrictionsStore['restrictions'] = []
  restrictionOptions: IWerRestrictionsStore['restrictionOptions'] = null
  _createRestrictions: IWerRestrictionsStore['_createRestrictions'] = []
  _updateRestrictions: IWerRestrictionsStore['_updateRestrictions'] = []
  _deleteRestrictions: IWerRestrictionsStore['_deleteRestrictions'] = []

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  reset: IWerRestrictionsStore['reset'] = () => {
    this.setIsLoading(true)
  }

  _enrichDataTabId: IWerRestrictionsStore['_enrichDataTabId'] = (arr) => {
    return deepCloneArray(arr).map((el) => {
      return { ...el, tabId: generateUUID() }
    })
  }

  _restrictionPayloadToPrePost: IWerRestrictionsStore['_restrictionPayloadToPrePost'] = (payload) => {
    const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
    const parameter = this.restrictionOptions?.parameters.find((el) => el.value === payload.parameter)
    const valueType = this.restrictionOptions?.valueTypes.find((el) => el.value === payload.valueType)
    const type = this.restrictionOptions?.types.find((el) => el.value === payload.type)
    const category = this.restrictionOptions?.categories.find((el) => el.value === payload.category)
    if (plantId !== undefined && parameter && type && category) {
      return {
        id: -1,
        tabId: generateUUID(),
        plantId: plantId,
        parameter: {
          code: parameter.value,
          title: parameter.label,
        },
        valueType: valueType
          ? {
              code: valueType.value,
              title: valueType.label,
            }
          : null,
        type: {
          code: type.value,
          title: type.label,
        },
        category: {
          code: category.value,
          title: category.label,
        },
        beginDate: category?.value !== 'TEMPORARY' && payload.beginDate ? format(payload.beginDate, "'--'MM-dd") : null,
        stopDate: category?.value !== 'TEMPORARY' && payload.stopDate ? format(payload.stopDate, "'--'MM-dd") : null,
        temporaryBeginDate:
          category?.value === 'TEMPORARY' && payload.beginDate ? format(payload.beginDate, 'yyyy-MM-dd') : null,
        temporaryEndDate:
          category?.value === 'TEMPORARY' && payload.stopDate ? format(payload.stopDate, 'yyyy-MM-dd') : null,
        minValue: payload.minValue,
        maxValue: payload.maxValue,
        comment: payload.comment,
      }
    }

    return null
  }

  getRestrictions: IWerRestrictionsStore['getRestrictions'] = async (plantId, date) => {
    try {
      const res = await api.calcModelWerManager.getRestrictions(plantId, date)
      runInAction(() => {
        const restrictions = this._enrichDataTabId<IWerRestriction>(res)
        this.restrictions = restrictions
        this.originalRestrictions = restrictions
      })
    } catch (e) {
      console.log(e)
    }
  }

  getRestrictionOptions: IWerRestrictionsStore['getRestrictionOptions'] = async () => {
    try {
      const restrictionOptionsOutput = await api.calcModelWerManager.getRestrictionOptions()
      this.restrictionOptions = Object.entries(restrictionOptionsOutput).reduce((acc, [key, value]) => {
        acc[key as keyof IWerRestrictionOptions] = value.map((v: IParameter) => ({
          value: v.code,
          label: v.title,
        }))

        return acc
      }, {} as IWerRestrictionOptions)
    } catch (e) {
      console.log(e)
    }
  }

  init: IWerRestrictionsStore['init'] = async (plantId, date) => {
    try {
      this.setIsLoading(true)
      await this.getRestrictions(plantId, date)
      await this.getRestrictionOptions()
    } catch (e) {
      console.log(e)
    } finally {
      setTimeout(() => {
        this.setIsLoading(false)
      }, TIME_LOADER)
      this.setIsLoading(false)
    }
  }

  setIsLoading: IWerRestrictionsStore['setIsLoading'] = (value) => {
    this.isLoading = value
  }

  addRestriction: IWerRestrictionsStore['addRestriction'] = (restriction) => {
    const item = this._restrictionPayloadToPrePost(restriction)
    if (item) {
      this.restrictions = [...this.restrictions, item]
      this._createRestrictions = [...this._createRestrictions, item]
      this.isEditing = true
    }
  }

  updateRestriction: IWerRestrictionsStore['updateRestriction'] = (restriction) => {
    const findIdx = this.restrictions.findIndex((c) => c.tabId === restriction.tabId)
    if (findIdx !== -1) {
      const item = this._restrictionPayloadToPrePost(restriction)
      const newItem = {
        ...this.restrictions[findIdx],
        ...item,
        id: this.restrictions[findIdx].id,
      }
      this.restrictions = this.restrictions.map((r, idx) => (idx === findIdx ? newItem : r))
      if (this.restrictions[findIdx].id !== -1) {
        const updFindIdx = this._updateRestrictions.findIndex((c) => c.tabId === restriction.tabId)
        if (updFindIdx !== -1) {
          this._updateRestrictions = this._updateRestrictions.map((r, idx) => (idx === updFindIdx ? newItem : r))
        } else {
          this._updateRestrictions = [...this._updateRestrictions, newItem]
        }
      } else {
        const createFindIdx = this._createRestrictions.findIndex((c) => c.tabId === restriction.tabId)
        if (createFindIdx !== -1) {
          this._createRestrictions = this._createRestrictions.map((r, idx) => (idx === createFindIdx ? newItem : r))
        }
      }
      this.isEditing = true
    }
  }

  deleteRestriction: IWerRestrictionsStore['deleteRestriction'] = (restriction) => {
    const find = this.restrictions.find((el) => el.tabId === restriction.tabId)
    const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
    if (find?.id !== -1 && plantId !== undefined) {
      this._deleteRestrictions = [
        ...this._deleteRestrictions.filter((el) => el.tabId !== restriction.tabId),
        restriction,
      ]
      this.isEditing = true
    }
    this.restrictions = this.restrictions.filter((el) => el.tabId !== restriction.tabId)
    this._createRestrictions = this._createRestrictions.filter((el) => el.tabId !== restriction.tabId)
    this._updateRestrictions = this._updateRestrictions.filter((el) => el.tabId !== restriction.tabId)
  }

  saveChanges: IWerRestrictionsStore['saveChanges'] = async () => {
    try {
      const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
      const date = format(this.rootStore.calcModelWerStore.date, 'yyyy-MM-dd')
      const restrictions: IWerRestrictionInput['restrictions'] = [
        ...this._createRestrictions.map((restriction) => {
          delete restriction.tabId

          return {
            action: 'CREATE' as TActions,
            restriction: {
              ...restriction,
              id: undefined,
            },
          }
        }),
        ...this._updateRestrictions.map((restriction) => {
          delete restriction?.tabId

          return {
            action: 'UPDATE' as TActions,
            restriction,
          }
        }),
        ...this._deleteRestrictions.map((restriction) => {
          delete restriction?.tabId

          return {
            action: 'DELETE' as TActions,
            restriction,
          }
        }),
      ]
      if (plantId) {
        const res = await api.calcModelWerManager.saveRestrictions(plantId, date, { restrictions })
        this.restrictions = this._enrichDataTabId<IWerRestriction>(res)
        this.originalRestrictions = this._enrichDataTabId<IWerRestriction>(res)
        this.isEditing = false
      }
    } catch (e) {
      console.log(e)
    } finally {
      this.resetChanges()
    }
  }

  resetChanges: IWerRestrictionsStore['resetChanges'] = () => {
    this.restrictions = this.originalRestrictions
    this._createRestrictions = []
    this._updateRestrictions = []
    this._deleteRestrictions = []
    this.isEditing = false
  }
}
