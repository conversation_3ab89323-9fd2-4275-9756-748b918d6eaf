import { IWerRestriction, IWerRestrictionOptions } from 'entities/api/calcModelPage.entities'
import { IEnrichedTabIdData } from 'entities/shared/common.entities'
import { RootStore } from 'stores/RootStore'

export type IWerRestrictionPrePost = IEnrichedTabIdData<IWerRestriction>

export interface IWerRestrictionsStore {
  rootStore: RootStore
  isLoading: boolean
  isEditing: boolean
  restrictions: IWerRestrictionPrePost[]
  originalRestrictions: IWerRestrictionPrePost[]
  restrictionOptions: IWerRestrictionOptions | null
  _createRestrictions: IWerRestrictionPrePost[]
  _updateRestrictions: IWerRestrictionPrePost[]
  _deleteRestrictions: IWerRestrictionPrePost[]

  reset: () => void
  _enrichDataTabId: <T extends object>(arr: Array<T>) => Array<T & { tabId: string }>
  _restrictionPayloadToPrePost: (payload: IWerRestrictionBasePayload) => IWerRestrictionPrePost | null
  getRestrictions: (plantId: number, date: string) => Promise<void>
  getRestrictionOptions: () => Promise<void>
  init: (plantId: number, date: string) => Promise<void>
  addRestriction: (restriction: IWerRestrictionBasePayload) => void
  updateRestriction: (restriction: IEnrichedTabIdData<IWerRestrictionBasePayload>) => void
  deleteRestriction: (restriction: IWerRestrictionPrePost) => void
  saveChanges: () => Promise<void>
  resetChanges: () => void
  setIsLoading: (value: boolean) => void
}

interface IWerRestrictionBasePayload {
  id: number
  parameter: string
  category: string
  type: string
  // --06-12
  beginDate: Date | null
  // --06-20
  stopDate: Date | null
  minValue: number | null
  maxValue: number | null
  valueType: string
  comment: string
}
